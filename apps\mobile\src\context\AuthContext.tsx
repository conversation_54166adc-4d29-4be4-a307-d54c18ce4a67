// src/context/AuthContext.tsx
import React, { createContext, useState, useEffect, useContext } from 'react';
import * as SecureStore from 'expo-secure-store';
import { jwtDecode } from 'jwt-decode';
import { Platform } from 'react-native';
import { CommonActions } from '@react-navigation/native';
import Toast from 'react-native-toast-message';

import axiosClient, { setAuthToken } from '../api/axiosClient';
import { navigationRef } from '../navigation/navigationRef';

export interface UserPayload {
  userId: string;
  fullName: string;
  email: string;
  role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
  exp: number;
  iat: number;
}

interface AuthContextType {
  user: UserPayload | null;
  token: string | null;
  loading: boolean;
  login: (token: string) => Promise<void>;
  logout: (showMessage?: boolean) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);
export { AuthContext };

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserPayload | null>(null);
  const [token, setTokenState] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    bootstrap();
  }, []);

  const bootstrap = async () => {
    try {
      if (Platform.OS === 'web') return setLoading(false);

      const storedToken = await SecureStore.getItemAsync('token');
      if (!storedToken) {
        setLoading(false);
        return;
      }

      const decoded = jwtDecode<UserPayload>(storedToken);
      console.log('🧾 Decoded token payload (bootstrap):', decoded);

      if (!decoded.role) {
        console.warn('❌ Token is missing role. Forcing logout.');
        await logout(true);
        return;
      }

      if (decoded.exp * 1000 < Date.now()) {
        console.warn('⚠️ Token expired. Clearing...');
        await logout(true);
      } else {
        setUser(decoded);
        setTokenState(storedToken);
        setAuthToken(storedToken);

        const success = navigateToRoleDashboard(decoded.role);
        if (!success) {
          console.warn('🚫 Role mismatch detected. Logging out...');
          await logout(true);
        }
      }
    } catch (err) {
      console.error('🔴 Bootstrap failed:', err);
      await logout(true);
    } finally {
      setLoading(false);
    }
  };

  const login = async (token: string) => {
    try {
      if (!token) throw new Error('Invalid token');

      await SecureStore.setItemAsync('token', token);
      const decoded = jwtDecode<UserPayload>(token);
      console.log('🧾 Decoded token payload (login):', decoded);

      if (!decoded.role) {
        console.warn('❌ Decoded token missing role. Logging out...');
        await logout(true);
        return;
      }

      setUser(decoded);
      setTokenState(token);
      setAuthToken(token);

      const success = navigateToRoleDashboard(decoded.role);
      if (!success) {
        console.warn('🚫 Role mismatch at login. Logging out...');
        await logout(true);
      }
    } catch (error) {
      console.error('🔴 Login failed:', error);
    }
  };

  const logout = async (showMessage = false) => {
    try {
      await SecureStore.deleteItemAsync('token');
      setUser(null);
      setTokenState(null);
      setAuthToken(null);

      console.log('👋 Logged out');

      if (showMessage) {
        Toast.show({
          type: 'info',
          text1: 'Session expired',
          text2: 'Please login again.',
          position: 'bottom',
        });
      }

      navigationRef.current?.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: 'Auth' }],
        })
      );
    } catch (error) {
      console.error('🔴 Logout failed:', error);
    }
  };

  const navigateToRoleDashboard = (role: UserPayload['role']): boolean => {
    let expectedStack: string;
    let firstScreen: string;

    switch (role) {
      case 'TECHNICIAN':
        expectedStack = 'TechnicianStack';
        firstScreen = 'TechnicianDrawer';
        break;
      case 'ADMIN':
        expectedStack = 'AdminStack';
        firstScreen = 'AdminDashboard';
        break;
      case 'HOMEOWNER':
      default:
        expectedStack = 'HomeownerStack';
        firstScreen = 'Home';
        break;
    }

    const currentState = navigationRef.current?.getRootState();
    const currentStack = currentState?.routes?.[0]?.name;

    if (currentStack && currentStack !== 'Auth' && currentStack !== expectedStack) {
      console.error(`🚨 Expected ${expectedStack} but found ${currentStack}`);
      return false;
    }

    navigationRef.current?.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: expectedStack,
            state: {
              routes: [{ name: firstScreen }],
            },
          },
        ],
      })
    );

    return true;
  };

  return (
    <AuthContext.Provider value={{ user, token, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('❌ useAuth must be used inside an <AuthProvider>');
  }
  return context;
};
