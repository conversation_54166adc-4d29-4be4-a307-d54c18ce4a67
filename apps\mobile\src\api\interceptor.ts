// 🔧 Explanation: Global Axios error logging and response interception

import axios from 'axios';

const api = axios.create({
  baseURL: process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000/api', // 🔧 Dynamic for dev/prod
  timeout: 10000,
});

api.interceptors.response.use(
  response => response,
  error => {
    console.error('[API ERROR]', error.response?.data || error.message); // 🔧 Debug info
    return Promise.reject(error);
  }
);

export default api;
