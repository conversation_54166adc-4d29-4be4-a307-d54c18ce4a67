// src/screens/Homeowner/HomeScreen.tsx
import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../context/AuthContext';
import { useJobs } from '../../context/JobContext';
import type { Job } from '../../types/job'; // ✅ import the Job type

const HomeScreen = () => {
  const navigation = useNavigation<any>();
  const { user, logout } = useAuth();
  const { myJobs = [], refreshJobs } = useJobs();

  const jobs: Job[] = myJobs; // ✅ ensure jobs are typed

  const firstName = user?.fullName?.split(' ')[0] || 'Homeowner';

  const ongoingJobs = jobs.filter(
    job => job.status === 'PENDING' || job.status === 'IN_PROGRESS'
  );
  const completedJobs = jobs.filter(job => job.status === 'COMPLETED');

  const lastCompleted = completedJobs.sort((a, b) =>
    new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  )[0];

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Welcome back, {firstName} 👋</Text>
        <TouchableOpacity onPress={() => logout(true)}>
          <Text style={styles.logout}>Log out</Text>
        </TouchableOpacity>
      </View>

      {/* Quick Actions */}
      <TouchableOpacity
        style={styles.actionButton}
        onPress={() => navigation.navigate('BookJob')}
      >
        <Text style={styles.actionText}>+ Book a Technician</Text>
      </TouchableOpacity>

      {/* Summary Cards */}
      <View style={styles.cardsRow}>
        <View style={styles.card}>
          <Text style={styles.cardLabel}>Total Jobs</Text>
          <Text style={styles.cardValue}>{jobs.length}</Text>
        </View>
        <View style={styles.card}>
          <Text style={styles.cardLabel}>Ongoing</Text>
          <Text style={styles.cardValue}>{ongoingJobs.length}</Text>
        </View>
        <View style={styles.card}>
          <Text style={styles.cardLabel}>Last Done</Text>
          <Text style={styles.cardValue}>
            {lastCompleted
              ? new Date(lastCompleted.updatedAt).toLocaleDateString()
              : 'N/A'}
          </Text>
        </View>
      </View>

      {/* Job List */}
      <Text style={styles.sectionTitle}>My Jobs</Text>

      <FlatList
        data={jobs}
        keyExtractor={(item) => item.id}
        onRefresh={refreshJobs}
        refreshing={false}
        contentContainerStyle={{ paddingBottom: 100 }}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.jobItem}
            onPress={() => navigation.navigate('JobDetails', { jobId: item.id })}
          >
            <Text style={styles.jobText}>
              #{item.id.slice(0, 6)} — {item.status}
            </Text>
            <Text style={styles.jobSub}>
              {new Date(item.createdAt).toLocaleDateString()}
            </Text>
          </TouchableOpacity>
        )}
      />
    </ScrollView>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f9f9f9', padding: 20 },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: { fontSize: 20, fontWeight: 'bold' },
  logout: { color: 'red', fontWeight: '600' },
  actionButton: {
    backgroundColor: '#007AFF',
    padding: 14,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 20,
  },
  actionText: { color: '#fff', fontWeight: 'bold', fontSize: 16 },
  cardsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  card: {
    flex: 1,
    backgroundColor: '#fff',
    marginHorizontal: 5,
    padding: 16,
    borderRadius: 12,
    elevation: 3,
    alignItems: 'center',
  },
  cardLabel: { fontSize: 14, color: '#777' },
  cardValue: { fontSize: 24, fontWeight: 'bold', marginTop: 4 },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  jobItem: {
    backgroundColor: '#fff',
    padding: 14,
    borderRadius: 10,
    marginBottom: 10,
    elevation: 2,
  },
  jobText: { fontWeight: 'bold', fontSize: 16 },
  jobSub: { color: '#666', fontSize: 12, marginTop: 4 },
});
