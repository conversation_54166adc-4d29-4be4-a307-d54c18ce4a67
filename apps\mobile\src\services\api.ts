import axios from 'axios';
import { EXPO_PUBLIC_API_URL } from '@env';

// ✅ Fallback to localhost if environment variable is undefined
const baseURL = EXPO_PUBLIC_API_URL || 'http://localhost:5000/api';

const instance = axios.create({
  baseURL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// ✅ Add auth token automatically if available
instance.interceptors.request.use(
  async (config) => {
    const token = await getAuthToken(); // You must define this
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Dummy token getter — replace with your actual logic if needed
const getAuthToken = async (): Promise<string | null> => {
  try {
    const token = await Promise.resolve(localStorage.getItem('token')); // or SecureStore
    return token;
  } catch {
    return null;
  }
};

export default instance;
