// backend/src/controllers/job.controller.ts

import { Request, Response } from 'express';
import { JobModel } from '../models/job.model';
import { AuthenticatedRequest } from '../middleware/auth.middleware';
import { JobStatus } from '../generated/prisma';
import { prisma } from '../config/db';
import { PaymentModel } from '../models/payment.model';
import Stripe from 'stripe';
import { env } from '../config/env';

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-04-30.basil',
});

export const JobController = {
  createJob: async (req: Request, res: Response): Promise<void> => {
    const { user } = req as AuthenticatedRequest;
    const { technicianId, issue, photoUrl, scheduledAt } = req.body;

    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    try {
      const job = await JobModel.createJob({
        userId: user.userId,
        technicianId,
        issue,
        photoUrl,
        scheduledAt: new Date(scheduledAt),
      });

      res.status(201).json({ job });
    } catch (err) {
      res.status(500).json({ message: 'Internal server error' });
    }
  },

  getMyJobs: async (req: Request, res: Response): Promise<void> => {
    const { user } = req as AuthenticatedRequest;

    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    try {
      const jobs = await JobModel.getJobsByUser(user.userId);
      res.status(200).json({ jobs });
    } catch (err) {
      res.status(500).json({ message: 'Internal server error' });
    }
  },

  getAssignedJobs: async (req: Request, res: Response): Promise<void> => {
    const { user } = req as AuthenticatedRequest;

    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    try {
      const jobs = await JobModel.getJobsByTechnician(user.userId);
      res.status(200).json({ jobs });
    } catch (err) {
      res.status(500).json({ message: 'Internal server error' });
    }
  },

  updateJobStatus: async (req: Request, res: Response): Promise<void> => {
    const { jobId } = req.params;
    const { status, photoUrl, proofImageKey } = req.body;

    if (!status || !['IN_PROGRESS', 'COMPLETED'].includes(status)) {
      res.status(400).json({ message: 'Invalid status' });
      return;
    }

    try {
      const imageReference = proofImageKey || photoUrl;
      const updated = await JobModel.updateStatusWithProof(
        jobId,
        status as JobStatus,
        imageReference
      );
      res.status(200).json({ job: updated });
    } catch (err) {
      console.error('❌ updateJobStatus error:', err);
      res.status(500).json({ message: 'Internal server error' });
    }
  },

  getJobDetails: async (req: Request, res: Response): Promise<void> => {
    const { jobId } = req.params;

    try {
      const job = await JobModel.getJobById(jobId);
      if (!job) {
        res.status(404).json({ message: 'Job not found' });
        return;
      }

      res.status(200).json({ job });
    } catch (err) {
      res.status(500).json({ message: 'Internal server error' });
    }
  },

  confirmJobCompletion: async (req: Request, res: Response): Promise<void> => {
    const { user } = req as AuthenticatedRequest;
    const { jobId } = req.params;

    if (!user || user.role !== 'HOMEOWNER') {
      res.status(403).json({ message: 'Only homeowners can confirm jobs' });
      return;
    }

    try {
      const job = await prisma.job.findUnique({ where: { id: jobId } });

      if (!job || job.userId !== user.userId) {
        res.status(404).json({ message: 'Job not found or not yours' });
        return;
      }

      if (job.status !== 'COMPLETED') {
        res.status(400).json({ message: 'Job must be completed first' });
        return;
      }

      const payment = await PaymentModel.getPaymentByJobId(jobId);
      if (!payment?.stripePaymentIntentId) {
        res.status(400).json({ message: 'No payment intent found' });
        return;
      }

      // ✅ Capture payment
      await stripe.paymentIntents.capture(payment.stripePaymentIntentId);

      // ✅ Mark as released
      await PaymentModel.markAsReleased(jobId);

      // ✅ Add job.confirmedAt timestamp
      await prisma.job.update({
        where: { id: jobId },
        data: { confirmedAt: new Date() },
      });

      res.status(200).json({ message: 'Job confirmed and payment released to technician' });
    } catch (err) {
      console.error('❌ Error in confirmJobCompletion:', err);
      res.status(500).json({ message: 'Failed to confirm and release payment' });
    }
  },
};
