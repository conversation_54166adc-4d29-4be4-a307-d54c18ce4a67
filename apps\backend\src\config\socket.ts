import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import http from 'http';

interface UserPayload {
  id: string;
  role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
  email: string;
  // add more fields if needed
}

interface AuthenticatedSocket extends Socket {
  user?: UserPayload;
}

export let io: Server;

/**
 * Initialise the global socket instance.
 * Attaches JWT auth guard and user room tracking.
 */
export const initSocket = (server: http.Server) => {
  io = new Server(server, {
    cors: { origin: '*' },
  });

  // JWT Authentication Middleware
  io.use((socket: AuthenticatedSocket, next) => {
    const token = socket.handshake.auth?.token;

    if (!token) {
      return next(new Error('Unauthenticated: No token'));
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as UserPayload;
      socket.user = decoded;
      next();
    } catch (err) {
      return next(new Error('Unauthenticated: Invalid token'));
    }
  });

  // Connection Handler
  io.on('connection', (socket: AuthenticatedSocket) => {
    const userId = socket.user?.id;
    if (!userId) {
      socket.disconnect(true);
      return;
    }

    console.log(`🔌 [Socket Connected] User: ${userId}`);

    // Join a private room to enable targeted pushes
    socket.join(`user:${userId}`);

    // TECHNICIAN accepts a job
    socket.on('job:accept', (data) => {
      const { userId: homeownerId, jobId, technicianName } = data;
      io.to(`user:${homeownerId}`).emit('job:accepted', {
        jobId,
        technicianName,
        acceptedAt: new Date().toISOString(),
      });
    });

    // Additional example: technician sends location
    socket.on('location:update', (data) => {
      const { jobId, lat, lng } = data;
      io.to(`job:${jobId}`).emit('technician:location', { lat, lng });
    });

    // Disconnect cleanup
    socket.on('disconnect', (reason) => {
      console.log(`❌ [Socket Disconnected] User: ${userId} - ${reason}`);
    });
  });
};
