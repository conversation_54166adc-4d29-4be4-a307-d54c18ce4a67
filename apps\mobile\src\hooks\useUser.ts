import { useEffect, useState } from 'react';

// Simulate auth state (replace with asyncStorage or API later)
export function useUser() {
  const [user, setUser] = useState<{ id: string; token: string } | null>(null);
  const [role, setRole] = useState<'HOMEOWNER' | 'TECHNICIAN' | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading user from async storage or backend
    const timer = setTimeout(() => {
      // Hardcoded values for now
      const fakeUser = { id: '123', token: 'mocked-jwt-token' };
      const fakeRole = 'HOMEOWNER'; // or 'TECHNICIAN'

      setUser(fakeUser);
      setRole(fakeRole);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return { user, role, loading };
}
