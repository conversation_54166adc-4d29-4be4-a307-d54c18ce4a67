// 📁 File: apps/web/src/pages/Dashboard.tsx
import React, { useEffect, useState } from 'react';
import axios from '@/services/api';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from 'react-toastify';

interface Metrics {
  jobsToday: number;
  totalRevenue: number;
  onlineTechnicians: number;
  disputes: number;
}

const Dashboard = () => {
  const [metrics, setMetrics] = useState<Metrics>({
    jobsToday: 0,
    totalRevenue: 0,
    onlineTechnicians: 0,
    disputes: 0,
  });

  const fetchMetrics = async () => {
    try {
      const res = await axios.get('/dashboard/metrics'); // TODO: Add actual backend route
      setMetrics(res.data as Metrics);
    } catch (err) {
      toast.error('Failed to load dashboard stats');
    }
  };

  useEffect(() => {
    fetchMetrics();
  }, []);

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Admin Dashboard</h1>

      {/* Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="border rounded p-4 shadow bg-white">
          <p className="text-sm text-gray-500">Jobs Today</p>
          <p className="text-2xl font-bold">{metrics.jobsToday}</p>
        </div>
        <div className="border rounded p-4 shadow bg-white">
          <p className="text-sm text-gray-500">Total Revenue</p>
          <p className="text-2xl font-bold">£{metrics.totalRevenue.toFixed(2)}</p>
        </div>
        <div className="border rounded p-4 shadow bg-white">
          <p className="text-sm text-gray-500">Online Technicians</p>
          <p className="text-2xl font-bold">{metrics.onlineTechnicians}</p>
        </div>
        <div className="border rounded p-4 shadow bg-white">
          <p className="text-sm text-gray-500">Disputes</p>
          <p className="text-2xl font-bold">{metrics.disputes}</p>
        </div>
      </div>

      {/* Quick Action Buttons */}
      <div className="flex gap-4 mt-8">
        <Button onClick={() => toast.info('Coming soon!')} className="bg-blue-600 text-white">
          Broadcast Announcement
        </Button>
        <Button onClick={() => toast.info('Coming soon!')} className="bg-green-600 text-white">
          Contact Top Technician
        </Button>
      </div>
    </div>
  );
};

export default Dashboard;
