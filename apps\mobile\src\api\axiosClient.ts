// src/api/axiosClient.ts
import axios from 'axios';
import Constants from 'expo-constants';

// Get API base URL from environment variables or fallback to localhost
const API_URL =
  process.env.EXPO_PUBLIC_API_URL ??
  (Constants.expoConfig?.extra as any)?.EXPO_PUBLIC_API_URL ??
  'http://localhost:5000';

// Create Axios instance
const axiosClient = axios.create({
  baseURL: `${API_URL}/api`, // ✅ Add '/api' here to match backend routes
  headers: {
    'Content-Type': 'application/json',
  },
});

// 🔐 Internal token holder
let authToken: string | null = null;

// ✅ External function to set the token
export const setAuthToken = (token: string | null) => {
  authToken = token;
};

// 🔁 Request interceptor to attach Authorization header
axiosClient.interceptors.request.use((config) => {
  if (authToken && config.headers) {
    config.headers['Authorization'] = `Bearer ${authToken}`;
  }
  return config;
});

export default axiosClient;
