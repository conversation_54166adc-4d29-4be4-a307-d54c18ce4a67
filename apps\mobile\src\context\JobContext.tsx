// src/context/JobContext.tsx

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from 'react';
import { JobAPI } from '../api/job.api';        // ✅ Import only the API
import type { Job } from '../types/job';        // ✅ Use unified shared type
import { useAuth } from './AuthContext';

type JobContextType = {
  myJobs: Job[];
  assignedJobs: Job[];
  fetchMyJobs: () => Promise<void>;
  fetchAssignedJobs: () => Promise<void>;
  refreshJobs: () => Promise<void>;
  loading: boolean;
};

const JobContext = createContext<JobContextType | undefined>(undefined);

export const JobProvider = ({ children }: { children: ReactNode }) => {
  const { token, user } = useAuth();
  const [myJobs, setMyJobs] = useState<Job[]>([]);
  const [assignedJobs, setAssignedJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  const fetchMyJobs = async () => {
    try {
      const data = await JobAPI.getUserJobs();
      console.log('📥 My Jobs fetched:', data.length);
      setMyJobs(data || []);
    } catch (error) {
      console.error('❌ Error fetching my jobs:', error);
      setMyJobs([]);
    }
  };

  const fetchAssignedJobs = async () => {
    try {
      const data = await JobAPI.getTechnicianJobs();
      console.log('🔧 Assigned Jobs fetched:', data.length);
      setAssignedJobs(data || []);
    } catch (error) {
      console.error('❌ Error fetching assigned jobs:', error);
      setAssignedJobs([]);
    }
  };

  const refreshJobs = async () => {
    if (!token || !user) {
      setMyJobs([]);
      setAssignedJobs([]);
      setLoading(false);
      return;
    }

    setLoading(true);

    try {
      if (user.role === 'TECHNICIAN') {
        await fetchAssignedJobs();
      } else {
        await fetchMyJobs();
      }
    } finally {
      setLoading(false);
    }
  };

  // ✅ Auto-refresh jobs when user or token changes
  useEffect(() => {
    refreshJobs();
  }, [token, user]);

  return (
    <JobContext.Provider
      value={{
        myJobs,
        assignedJobs,
        fetchMyJobs,
        fetchAssignedJobs,
        refreshJobs,
        loading,
      }}
    >
      {children}
    </JobContext.Provider>
  );
};

export const useJobs = (): JobContextType => {
  const context = useContext(JobContext);
  if (!context) {
    throw new Error('❌ useJobs must be used within a JobProvider');
  }
  return context;
};
