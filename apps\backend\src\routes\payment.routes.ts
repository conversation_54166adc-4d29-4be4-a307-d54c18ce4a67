// src/routes/payment.routes.ts

import { Router } from 'express';
import { authMiddleware } from '../middleware/auth.middleware';
import { requireRole } from '../middleware/role.middleware';
import { asyncHandler } from '../utils/asyncHandler';
import { PaymentController } from '../controllers/payment.controller';

const router = Router();

// 🔔 Stripe webhook – no auth required
router.post(
  '/webhook',
  PaymentController.handleStripeWebhook
);

// 💳 Create payment intent – Homeowners only
router.post(
  '/',
  authMiddleware,
  requireRole(['HOMEOWNER']),
  asyncHandler(PaymentController.createPayment)
);

// 📄 Get payment info – Homeowners or Technicians
router.get(
  '/:jobId',
  authMiddleware,
  requireRole(['HOMEOWNER', 'TECHNICIAN']),
  asyncHandler(PaymentController.getPayment)
);

// ✅ Capture payment – called after homeowner confirms
router.post(
  '/:jobId/capture',
  authMiddleware,
  asyncHandler(PaymentController.capturePayment)
);

// 🔐 Admin override – force release payment
router.patch(
  '/:jobId/release-manually',
  authMiddleware,
  requireRole(['ADMIN']),
  asyncHandler(PaymentController.releaseManually)
);

// 🚫 Freeze payment (dispute) – Admin only
router.patch(
  '/:jobId/freeze',
  authMiddleware,
  requireRole(['ADMIN']),
  asyncHandler(PaymentController.freezePayment)
);

// 🚪 Admin unfreeze – reverse dispute freeze
router.patch(
  '/:jobId/unfreeze',
  authMiddleware,
  requireRole(['ADMIN']),
  asyncHandler(PaymentController.unfreezePayment)
);

export default router;
