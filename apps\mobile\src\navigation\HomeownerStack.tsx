//src/navigation/HomownerStack.tsx
import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import HomeScreen from '../screens/Homeowner/HomeScreen';
import MyJobsScreen from '../screens/Homeowner/MyJobsScreen';
import JobDetailsScreen from '../screens/Homeowner/JobDetailsScreen';
import PaymentScreen from '../screens/Homeowner/PaymentScreen';
import BookJobScreen from '../screens/Homeowner/BookJobScreen';

const Stack = createNativeStackNavigator();

const HomeownerStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="Home" component={HomeScreen} />
    <Stack.Screen name="MyJobs" component={MyJobsScreen} />
    <Stack.Screen name="JobDetails" component={JobDetailsScreen} />
    <Stack.Screen name="Payment" component={PaymentScreen} />
    <Stack.Screen name="BookJob" component={BookJobScreen} /> 
  </Stack.Navigator>
);

export default HomeownerStack;
