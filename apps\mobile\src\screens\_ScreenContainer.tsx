import React from 'react';
import { SafeAreaView, View, StyleSheet } from 'react-native';
import { theme } from '../theme';

export const ScreenContainer = ({ children }: { children: React.ReactNode }) => (
  <SafeAreaView style={styles.safe}>
    <View style={styles.inner}>{children}</View>
  </SafeAreaView>
);

const styles = StyleSheet.create({
  safe: { flex: 1, backgroundColor: theme.colors.white },
  inner: { flex: 1, padding: theme.spacing.m },
});
