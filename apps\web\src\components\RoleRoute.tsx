//apps/web/src/components/RoleRoute.tsx
import React, { JSX } from 'react';
import { Navigate } from 'react-router-dom';

type Props = {
  allowedRoles: string[];
  children: JSX.Element;
};

const RoleRoute = ({ allowedRoles, children }: Props) => {
  const token = localStorage.getItem('token');
  const role = localStorage.getItem('role');

  if (!token || !role || !allowedRoles.includes(role)) {
    return <Navigate to="/login" />;
  }

  return children;
};

export default RoleRoute;
