// src/utils/useDeepLinkHandler.ts

import { useEffect } from 'react';
import * as Linking from 'expo-linking';
import { useAuth } from '../context/AuthContext';
import { navigationRef } from '../navigation/navigationRef';

export default function useDeepLinkHandler() {
  const { token, user } = useAuth();

  useEffect(() => {
    if (!token || !user) return;

    const handleUrl = ({ url }: { url: string }) => {
      console.log('🔗 Deep link received:', url);
      const parsed = Linking.parse(url);
      const path = parsed.path || '';

      if (!path) {
        console.log('ℹ️ No specific path. Staying on current screen.');
        return;
      }

      const isTechnician = user.role === 'TECHNICIAN';
      const isHomeowner = user.role === 'HOMEOWNER';

      if (path.startsWith('technician') && isTechnician) {
        navigationRef.current?.navigate('TechnicianStack');
      } else if (path.startsWith('my-jobs') && isHomeowner) {
        navigationRef.current?.navigate('HomeownerStack');
      } else {
        console.warn('⚠️ Deep link path not recognized. No navigation triggered.');
        // We don't deny harmless links like root URL
      }
    };

    const sub = Linking.addEventListener('url', handleUrl);

    Linking.getInitialURL().then((url) => {
      if (url) handleUrl({ url });
    });

    return () => sub.remove();
  }, [token, user]);
}
