{"name": "ctron-backend", "version": "1.0.0", "type": "commonjs", "main": "index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "seed": "ts-node prisma/seed.ts", "test": "jest --runInBand", "release:expired-jobs": "ts-node scripts/autoReleaseCompletedJobs.ts"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.812.0", "@aws-sdk/s3-request-presigner": "^3.812.0", "@faker-js/faker": "^9.7.0", "@prisma/client": "^6.6.0", "@react-native-async-storage/async-storage": "^2.1.2", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^3.1.2", "node-cron": "^4.0.5", "nodemailer": "^6.10.1", "openai": "^4.95.1", "socket.io": "^4.8.1", "stripe": "^18.0.0", "uuid": "^11.1.0", "zod": "^3.24.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.2", "@types/nodemailer": "^6.4.17", "@types/socket.io": "^3.0.1", "jest": "^29.7.0", "nodemon": "^3.1.9", "prisma": "^6.6.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}