// ✅ File: backend/src/controllers/dashboard.controller.ts

import { Request, Response } from 'express';
import { prisma } from '../config/db';

export const DashboardController = {
  getMetrics: async (req: Request, res: Response): Promise<void> => {
    try {
      const totalRevenue = await prisma.payment.aggregate({
        _sum: { amount: true },
        where: { isReleased: true }
      });

      const jobsToday = await prisma.job.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)), // start of today
          },
        },
      });

      const onlineTechs = await prisma.technician.count({
        where: {
          isAvailable: true
        }
      });

      res.status(200).json({
        totalRevenue: totalRevenue._sum.amount || 0,
        jobsToday,
        onlineTechnicians: onlineTechs,
      });
    } catch (err) {
      console.error('❌ Dashboard metrics error:', err);
      res.status(500).json({ message: 'Failed to load dashboard metrics' });
    }
  }
};
