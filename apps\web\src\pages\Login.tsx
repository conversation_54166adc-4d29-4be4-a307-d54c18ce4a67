// 📁 File: apps/web/src/pages/Login.tsx

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../services/api'; // Use your configured Axios instance

// Define the expected shape of the login response
type LoginResponse = {
  token: string;
  user: {
    role: string;
  };
};


const Login = () => {
  // Local state for form fields and error display
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const navigate = useNavigate();

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Call login API with typed response
      const response = await axios.post<LoginResponse>('/auth/login', {
        email,
        password,
      });

      // Save JWT token in localStorage
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('role', response.data.user.role);

      // Redirect to dashboard after successful login
      navigate('/dashboard');
    } catch (err: any) {
      // Show appropriate error message
      setError(err.response?.data?.message || 'Login failed');
    }
  };

  return (
    <div className="flex flex-col items-center justify-center h-screen bg-gray-100">
      <form
        onSubmit={handleSubmit}
        className="bg-white p-6 rounded shadow-md w-full max-w-sm"
      >
        <h2 className="text-xl font-bold mb-4">Admin Login</h2>

        {error && <p className="text-red-500 mb-2">{error}</p>}

        <input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full border p-2 rounded mb-2"
          required
        />

        <input
          type="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="w-full border p-2 rounded mb-4"
          required
        />

        <button type="submit" className="w-full bg-blue-600 text-white py-2 rounded">
          Login
        </button>
      </form>
    </div>
  );
};

export default Login;
