// src/navigation/index.tsx

import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { useAuth } from '../context/AuthContext';
import AuthStack from './AuthStack';
import HomeownerStack from './HomeownerStack';
import TechnicianStack from './TechnicianStack';

const Stack = createNativeStackNavigator();

const RootNavigation = () => {
  const { user, loading } = useAuth();

  if (loading) return null;

  return (
    <NavigationContainer>
      {user ? (
        user.role === 'TECHNICIAN' ? <TechnicianStack /> : <HomeownerStack />
      ) : (
        <AuthStack />
      )}
    </NavigationContainer>
  );
};

export default RootNavigation;
