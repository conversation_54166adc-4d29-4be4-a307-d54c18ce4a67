{"name": "ctron-web", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.8.4", "jwt-decode": "^4.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.1", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "zustand": "^4.3.9"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.3.5"}}