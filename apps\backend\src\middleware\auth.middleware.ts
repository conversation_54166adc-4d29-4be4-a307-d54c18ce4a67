// apps/backend/src/middleware/auth.middleware.ts

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { env } from '../config/env';

export interface AuthenticatedRequest extends Request {
  user?: {
    userId: string;
    role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
    email?: string;
    fullName?: string;
  };
}

export const authMiddleware = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const authHeader = req.headers.authorization;

  if (!authHeader?.startsWith('Bearer ')) {
    res.status(401).json({ message: 'Unauthorized: No token provided' });
    return;
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, env.JWT_SECRET) as AuthenticatedRequest['user'];

    if (!decoded?.userId || !decoded?.role) {
      res.status(403).json({ message: 'Forbidden: Invalid token payload' });
      return;
    }

    req.user = decoded;
    next();
  } catch (err) {
    console.error('❌ Invalid JWT:', err);
    res.status(403).json({ message: 'Forbidden: Invalid token' });
    return;
  }
};
