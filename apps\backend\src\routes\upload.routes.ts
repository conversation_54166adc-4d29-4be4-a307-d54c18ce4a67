// src/routes/upload.routes.ts

import { Router } from 'express';
import { UploadController } from '../controllers/upload.controller';
import { authMiddleware } from '../middleware/auth.middleware';
import { requireRole } from '../middleware/role.middleware';

const router = Router();

// Only technicians can request a presigned S3 upload URL
router.post(
  '/presign',
  authMiddleware,
  requireRole(['TECHNICIAN']),
  UploadController.getPresignedUrl
);

export default router;
