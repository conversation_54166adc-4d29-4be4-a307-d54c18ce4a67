// src/navigation/RootNavigator.tsx
import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { NavigationContainer } from '@react-navigation/native';
import { navigationRef } from './navigationRef';

// Temporary, import your stacks here (you can adjust if needed):
import AuthStack from './AuthStack';
import HomeownerStack from './HomeownerStack';
import TechnicianStack from './TechnicianStack';
import AdminStack from './AdminStack';

// Types for the routes
export type RootStackParamList = {
  Auth: undefined;
  HomeownerStack: undefined;
  TechnicianStack: undefined;
  AdminStack: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

export const RootNavigator = () => {
  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Auth" component={AuthStack} />
        <Stack.Screen name="HomeownerStack" component={HomeownerStack} />
        <Stack.Screen name="TechnicianStack" component={TechnicianStack} />
        <Stack.Screen name="AdminStack" component={AdminStack} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};
