// apps/web/src/pages/admin/JobDetails.tsx

import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import axios from '@/services/api';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from 'react-toastify';

interface Job {
  id: string;
  issue: string;
  status: string;
  photoUrl?: string;
  proofImageKey?: string;
  createdAt: string;
  scheduledAt: string;
  completedAt?: string;
  user: { fullName: string };
  technician?: { user: { fullName: string } };
  payment?: {
    isReleased: boolean;
    isFrozen: boolean;
    freezeReason?: string;
  };
}

const JobDetails = () => {
  const { id } = useParams<{ id: string }>();
  const [job, setJob] = useState<Job | null>(null);

  const fetchJob = async () => {
    try {
      const res = await axios.get<{ job: Job }>(`/jobs/${id}`);
      setJob(res.data.job as Job);
    } catch {
      toast.error('Failed to load job');
    }
  };

  const handleManualRelease = async () => {
    try {
      await axios.patch(`/payments/${job?.id}/release-manually`);
      toast.success('Manual release successful');
      fetchJob();
    } catch {
      toast.error('Failed to release payment');
    }
  };

  const handleFreeze = async () => {
    try {
      const reason = prompt('Reason for freezing payment?');
      if (!reason) return;
      await axios.patch(`/payments/${job?.id}/freeze`, { reason });
      toast.success('Payment frozen');
      fetchJob();
    } catch {
      toast.error('Failed to freeze payment');
    }
  };

  const handleUnfreeze = async () => {
    try {
      await axios.patch(`/payments/${job?.id}/unfreeze`);
      toast.success('Payment unfrozen');
      fetchJob();
    } catch {
      toast.error('Failed to unfreeze');
    }
  };

  useEffect(() => {
    fetchJob();
  }, [id]);

  if (!job) return <p className="p-6">Loading job details...</p>;

  return (
    <div className="p-6 space-y-4">
      <h1 className="text-2xl font-bold mb-2">Job Detail</h1>

      <div className="bg-white p-4 rounded shadow space-y-2">
        <p><strong>Issue:</strong> {job.issue}</p>
        <p><strong>Status:</strong> <Badge>{job.status}</Badge></p>
        <p><strong>Customer:</strong> {job.user.fullName}</p>
        {job.technician && <p><strong>Technician:</strong> {job.technician.user.fullName}</p>}
        <p><strong>Scheduled At:</strong> {new Date(job.scheduledAt).toLocaleString()}</p>
        {job.completedAt && <p><strong>Completed At:</strong> {new Date(job.completedAt).toLocaleString()}</p>}

        {job.photoUrl && (
          <div>
            <strong>Initial Photo:</strong>
            <img src={job.photoUrl} alt="Initial" className="mt-2 w-full max-w-md rounded" />
          </div>
        )}

        {job.proofImageKey && (
          <div>
            <strong>Proof of Completion:</strong>
            <img
              src={`https://${process.env.NEXT_PUBLIC_AWS_BUCKET}.s3.amazonaws.com/${job.proofImageKey}`}
              alt="Proof"
              className="mt-2 w-full max-w-md rounded"
            />
          </div>
        )}

        <div className="flex gap-4 mt-4">
          {job.payment?.isReleased && <Badge className="bg-green-500 text-white">Payment Released</Badge>}
          {job.payment?.isFrozen && <Badge className="bg-red-500 text-white">Frozen: {job.payment.freezeReason}</Badge>}
        </div>

        {/* Admin Actions */}
        {!job.payment?.isReleased && (
          <div className="flex gap-4 mt-4">
            {!job.payment?.isFrozen ? (
              <Button onClick={handleFreeze} className="bg-red-600 text-white">Freeze Payment</Button>
            ) : (
              <Button onClick={handleUnfreeze} className="bg-yellow-500 text-white">Unfreeze</Button>
            )}
            <Button onClick={handleManualRelease} className="bg-green-700 text-white">Manual Release</Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default JobDetails;
