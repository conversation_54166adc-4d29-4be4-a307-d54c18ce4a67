import React, { useEffect } from 'react';
import { ActivityIndicator, View, Text } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useAuth } from '../context/AuthContext';
import useDeep<PERSON>inkHandler from '../utils/useDeepLinkHandler';
import { navigationRef } from './navigationRef';

import AuthStack from './AuthStack';
import TechnicianStack from './TechnicianStack'; // ✅ FIXED: use the full stack, not drawer
import HomeownerStack from './HomeownerStack';

const Root = createNativeStackNavigator();

const AccessDenied = () => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <Text style={{ fontSize: 18, color: 'red' }}>Access Denied: Role Mismatch</Text>
  </View>
);

export default function AppNavigator() {
  const { token, user, loading } = useAuth();
  useDeepLinkHandler();

  if (loading) {
    return (
      <SafeAreaProvider>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" />
        </View>
      </SafeAreaProvider>
    );
  }

  return (
    <SafeAreaProvider>
      <NavigationContainer ref={navigationRef}>
        <Root.Navigator screenOptions={{ headerShown: false }}>
          {!token || !user ? (
            <Root.Screen name="Auth" component={AuthStack} />
          ) : user.role === 'TECHNICIAN' ? (
            <Root.Screen name="TechnicianStack" component={TechnicianStack} />
          ) : user.role === 'HOMEOWNER' ? (
            <Root.Screen name="HomeownerStack" component={HomeownerStack} />
          ) : (
            <Root.Screen name="AccessDenied" component={AccessDenied} />
          )}
        </Root.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}
