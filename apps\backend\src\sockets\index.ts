// src/sockets/index.ts

import { Server } from 'socket.io';
import { registerTechnicianSockets } from './technician.socket';
import { registerJobSockets } from './job.socket';

export const registerSockets = (io: Server) => {
  io.on('connection', (socket) => {
    console.log('🟢 Socket connected:', socket.id);

    registerTechnicianSockets(io, socket);
    registerJobSockets(io, socket);

    socket.on('disconnect', () => {
      console.log('🔴 Socket disconnected:', socket.id);
    });
  });
};
