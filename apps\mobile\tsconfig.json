{"extends": "expo/tsconfig.base", "compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "jsx": "react-native", "strict": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "resolveJsonModule": true, "moduleResolution": "node", "isolatedModules": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "baseUrl": "./src", "paths": {"@/*": ["*"]}}, "include": ["src", "App.tsx", "src/types/env.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js", "coverage", "android", "ios", "web-build"]}