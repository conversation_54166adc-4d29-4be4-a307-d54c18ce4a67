// metro.config.js
const { getDefaultConfig } = require('@expo/metro-config');

const config = getDefaultConfig(__dirname);

// Force Node modules to resolve to empty or safe fallbacks
config.resolver.extraNodeModules = {
  crypto: require.resolve('expo-crypto'), // OR false if not used
  stream: require.resolve('readable-stream'),
  http: require.resolve('stream-http'),
  https: require.resolve('https-browserify'),
  os: require.resolve('os-browserify/browser'),
  path: require.resolve('path-browserify'),
  zlib: require.resolve('browserify-zlib'),
};

module.exports = config;
