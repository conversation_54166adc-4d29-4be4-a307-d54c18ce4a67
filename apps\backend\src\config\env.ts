// apps/backend/src/config/env.ts
import dotenv from 'dotenv';
dotenv.config();

if (!process.env.JWT_SECRET) throw new Error('❌ Missing JWT_SECRET in .env');
if (!process.env.DATABASE_URL) throw new Error('❌ Missing DATABASE_URL in .env');
if (!process.env.STRIPE_SECRET_KEY) throw new Error('❌ Missing STRIPE_SECRET_KEY in .env');
if (!process.env.STRIPE_WEBHOOK_SECRET) throw new Error('❌ Missing STRIPE_WEBHOOK_SECRET in .env');

export const env = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: process.env.PORT || '5000',
  JWT_SECRET: process.env.JWT_SECRET!,
  DATABASE_URL: process.env.DATABASE_URL!,
  STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY!,
  STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET!,
  OPENAI_API_KEY: process.env.OPENAI_API_KEY || '',
};
