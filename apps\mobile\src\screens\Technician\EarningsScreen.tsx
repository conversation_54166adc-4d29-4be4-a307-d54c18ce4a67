// src/screens/Technician/EarningsScreen.tsx

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import PaymentAPI from '../../api/payment.api'; // ✅ default export

interface Earning {
  amount: number;
  date: string;
  jobId: string;
}

const EarningsScreen = () => {
  const [earnings, setEarnings] = useState<Earning[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchEarnings = async () => {
    try {
      const res = await PaymentAPI.getEarnings(); // ✅ direct call to .getEarnings()
      setEarnings(res.earnings || []);
    } catch (err: any) {
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEarnings();
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Earnings</Text>

      {loading ? (
        <ActivityIndicator size="large" color="#004AAD" />
      ) : (
        <FlatList
          data={earnings}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({ item }) => (
            <View style={styles.card}>
              <Text style={styles.amount}>
                £{item.amount?.toFixed(2) ?? '0.00'}
              </Text>
              <Text style={styles.date}>
                {item.date
                  ? new Date(item.date).toLocaleDateString()
                  : 'Unknown date'}
              </Text>
              <Text style={styles.jobId}>
                Job #{item.jobId?.slice(0, 8) ?? 'N/A'}
              </Text>
            </View>
          )}
        />
      )}
    </View>
  );
};

export default EarningsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  card: {
    backgroundColor: '#f3f3f3',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  amount: {
    fontSize: 18,
    fontWeight: '600',
  },
  date: {
    color: '#555',
    marginTop: 4,
  },
  jobId: {
    marginTop: 4,
    fontSize: 12,
    color: '#888',
  },
});
