// src/services/uploadService.ts

import axios from 'axios';
import { getAccessToken } from './authService';

/**
 * Get a presigned S3 URL from the backend for direct image upload
 * @param fileType MIME type of the file (e.g. 'image/jpeg')
 * @returns { url: string, key: string }
 */
export const getPresignedUrl = async (fileType: string): Promise<{ url: string; key: string }> => {
  const token = await getAccessToken(); // Secure JWT from AsyncStorage

  const response = await axios.post(
    `${process.env.EXPO_PUBLIC_API_URL}/api/uploads/presign`,
    { fileType },
    {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    }
  );

  return response.data; // Contains { url, key }
};

/**
 * Upload a file to the signed S3 URL
 * @param url Signed URL from S3
 * @param fileUri Local file URI (from ImagePicker)
 * @param fileType MIME type (e.g. 'image/jpeg')
 */
export const uploadToS3 = async (url: string, fileUri: string, fileType: string): Promise<boolean> => {
  const file = await fetch(fileUri);
  const blob = await file.blob();

  const result = await fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': fileType,
    },
    body: blob,
  });

  if (!result.ok) {
    throw new Error('Upload to S3 failed');
  }

  return true;
};
