// src/controllers/payment.controller.ts

import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'stripe';
import { env } from '../config/env';
import { PaymentModel } from '../models/payment.model';
import { prisma } from '../config/db';
import { AuthenticatedRequest } from '../middleware/auth.middleware';

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-04-30.basil',
});

export const PaymentController = {
  /**
   * Step 1: Create Stripe PaymentIntent & save to DB
   */
  createPayment: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { user } = req as AuthenticatedRequest;
    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const { jobId, amount, currency = 'GBP' } = req.body;

    try {
      const intent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100),
        currency,
        metadata: { jobId },
        automatic_payment_methods: { enabled: true },
      });

      await PaymentModel.createPayment(
        jobId,
        user.userId,
        amount,
        currency,
        intent.id
      );

      res.status(200).json({ clientSecret: intent.client_secret });
    } catch (err) {
      console.error('❌ Error creating payment intent:', err);
      next(err);
    }
  },

  /**
   * Step 2: Stripe webhook handler to mark payment as successful
   */
  handleStripeWebhook: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const sig = req.headers['stripe-signature'];

    if (!sig || typeof sig !== 'string') {
      res.status(400).send('Missing Stripe signature');
      return;
    }

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(
        req.body,
        sig,
        env.STRIPE_WEBHOOK_SECRET
      );
    } catch (e: any) {
      console.error('⚠️ Invalid Stripe signature:', e.message);
      res.status(400).send(`Webhook Error: ${e.message}`);
      return;
    }

    console.log(`🔔 Webhook received: ${event.type}`);

    if (event.type === 'payment_intent.succeeded') {
      const pi = event.data.object as Stripe.PaymentIntent;
      const jobId = pi.metadata.jobId;

      try {
        await PaymentModel.markAsPaid(jobId);
        console.log(`✅ Payment confirmed for job ${jobId}`);
      } catch (err) {
        console.error(`❌ Failed to mark payment as paid for job ${jobId}`, err);
      }
    }

    res.status(200).json({ received: true });
  },

  /**
   * Step 3: Get payment by jobId
   */
  getPayment: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { jobId } = req.params;

    try {
      const payment = await PaymentModel.getPaymentByJobId(jobId);

      if (!payment) {
        res.status(404).json({ message: 'Payment not found' });
        return;
      }

      res.status(200).json({ payment });
    } catch (err) {
      next(err);
    }
  },

  /**
   * Step 4: Capture payment on homeowner confirmation
   */
  capturePayment: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { jobId } = req.params;

    try {
      const payment = await PaymentModel.getPaymentByJobId(jobId);

      if (!payment || !payment.stripePaymentIntentId) {
        res.status(404).json({ message: 'Payment or intent not found' });
        return;
      }

      await stripe.paymentIntents.capture(payment.stripePaymentIntentId);
      await PaymentModel.markAsReleased(jobId);

      res.status(200).json({ message: 'Payment captured and released' });
    } catch (err) {
      console.error('❌ Stripe capture error:', err);
      next(err);
    }
  },

  /**
   * Step 5: Admin override release
   */
  releaseManually: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { jobId } = req.params;

    try {
      const payment = await PaymentModel.getPaymentByJobId(jobId);

      if (!payment || payment.isReleased) {
        res.status(400).json({ message: 'Payment already released or not found' });
        return;
      }

      await stripe.paymentIntents.capture(payment.stripePaymentIntentId);
      await PaymentModel.markAsReleased(jobId);

      await prisma.job.update({
        where: { id: jobId },
        data: {
          confirmedAt: new Date(),
        },
      });

      res.status(200).json({ message: 'Manual payment release successful' });
    } catch (err) {
      console.error('❌ Admin manual release failed:', err);
      next(err);
    }
  },

  /**
   * Step 6: Admin dispute – freeze payment
   */
  freezePayment: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { jobId } = req.params;
    const { reason } = req.body;

    try {
      const payment = await PaymentModel.getPaymentByJobId(jobId);

      if (!payment) {
        res.status(404).json({ message: 'Payment not found' });
        return;
      }

      if (payment.isReleased) {
        res.status(400).json({ message: 'Payment already released' });
        return;
      }

      if (payment.isFrozen) {
        res.status(400).json({ message: 'Payment already frozen' });
        return;
      }

      await PaymentModel.freezePayment(jobId, reason);
      res.status(200).json({ message: 'Payment frozen successfully' });
    } catch (err) {
      console.error('❌ Error freezing payment:', err);
      next(err);
    }
  },

  /**
   * Step 7: Admin unfreeze – resolve dispute
   */
  unfreezePayment: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { jobId } = req.params;

    try {
      const payment = await PaymentModel.getPaymentByJobId(jobId);

      if (!payment) {
        res.status(404).json({ message: 'Payment not found' });
        return;
      }

      if (!payment.isFrozen) {
        res.status(400).json({ message: 'Payment is not frozen' });
        return;
      }

      await PaymentModel.unfreezePayment(jobId);
      res.status(200).json({ message: 'Payment unfrozen successfully' });
    } catch (err) {
      console.error('❌ Error unfreezing payment:', err);
      next(err);
    }
  },
};

export const stripeWebhookHandler = PaymentController.handleStripeWebhook;
