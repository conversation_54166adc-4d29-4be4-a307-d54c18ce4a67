// src/screens/Homeowner/TrackTechnicianScreen.tsx

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const TrackTechnicianScreen = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Live Technician Tracking</Text>
      <Text style={styles.text}>Coming soon: real-time location tracking and ETA updates.</Text>
    </View>
  );
};

export default TrackTechnicianScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
    backgroundColor: '#f9f9f9',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  text: {
    fontSize: 16,
    color: '#444',
  },
});
