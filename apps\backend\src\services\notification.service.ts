// src/services/notification.service.ts

import nodemailer from 'nodemailer';
import axios from 'axios';

export const NotificationService = {
  sendEmail: async (
    to: string,
    subject: string,
    text: string,
    html?: string
  ) => {
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    await transporter.sendMail({
      from: `"CTRON App" <${process.env.EMAIL_USER}>`,
      to,
      subject,
      text,
      html,
    });
  },
};

/**
 * Sends push notification via Expo Push API
 */
export async function sendPush(to: string, title: string, body: string) {
  await axios.post('https://exp.host/--/api/v2/push/send', {
    to,
    title,
    body,
  });
}
