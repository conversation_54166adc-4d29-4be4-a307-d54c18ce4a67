// apps/web/src/pages/admin/Jobs.tsx

import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'react-toastify';

interface Payment {
  isReleased: boolean;
  isFrozen: boolean;
  freezeReason?: string;
}

interface Job {
  id: string;
  issue: string;
  status: string;
  createdAt: string;
  scheduledAt: string;
  technician?: {
    user: {
      fullName: string;
    };
  };
  user: {
    fullName: string;
  };
  payment?: Payment;
}

const JobsPage: React.FC = () => {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchJobs = async () => {
    try {
      setLoading(true);
      const res = await axios.get<{ jobs: Job[] }>('/api/jobs');
      setJobs(res.data.jobs);
    } catch (err: any) {
      console.error(err?.response?.data || err.message);
      toast.error('Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };

  const handleFreeze = async (jobId: string) => {
    try {
      const reason = prompt('Enter freeze reason:');
      if (!reason) return;
      await axios.patch(`/api/payments/${jobId}/freeze`, { reason });
      toast.success('Payment frozen');
      fetchJobs();
    } catch {
      toast.error('Freeze failed');
    }
  };

  const handleUnfreeze = async (jobId: string) => {
    try {
      await axios.patch(`/api/payments/${jobId}/unfreeze`);
      toast.success('Payment unfrozen');
      fetchJobs();
    } catch {
      toast.error('Unfreeze failed');
    }
  };

  const handleManualRelease = async (jobId: string) => {
    try {
      await axios.patch(`/api/payments/${jobId}/release-manually`);
      toast.success('Payment released manually');
      fetchJobs();
    } catch {
      toast.error('Manual release failed');
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Job Payments</h1>

      {loading ? (
        <p>Loading...</p>
      ) : (
        <div className="space-y-4">
          {jobs.map((job) => (
            <div key={job.id} className="border rounded p-4 shadow-sm">
              <div className="flex justify-between">
                <div>
                  <p className="font-semibold">{job.issue}</p>
                  <p className="text-sm text-gray-600">
                    Scheduled: {new Date(job.scheduledAt).toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-600">
                    Customer: {job.user.fullName}
                  </p>
                  {job.technician && (
                    <p className="text-sm text-gray-600">
                      Technician: {job.technician.user.fullName}
                    </p>
                  )}
                  <div className="mt-2 space-x-2">
                    <Badge>{job.status}</Badge>
                    {job.payment?.isReleased && (
                      <Badge className="bg-green-500 text-white">Released</Badge>
                    )}
                    {job.payment?.isFrozen && (
                      <Badge className="bg-red-500 text-white">Frozen</Badge>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  {!job.payment?.isReleased && (
                    <>
                      {!job.payment?.isFrozen ? (
                        <Button onClick={() => handleFreeze(job.id)} className="w-full">
                          Freeze
                        </Button>
                      ) : (
                        <Button
                          onClick={() => handleUnfreeze(job.id)}
                          variant="outline"
                          className="w-full"
                        >
                          Unfreeze
                        </Button>
                      )}
                      <Button
                        onClick={() => handleManualRelease(job.id)}
                        variant="secondary"
                        className="w-full"
                      >
                        Manual Release
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default JobsPage;
