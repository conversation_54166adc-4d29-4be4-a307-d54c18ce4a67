// 📁 File: apps/web/src/pages/admin/Settings.tsx

import React, { useEffect, useState } from 'react';
import axios from '@/services/api';
import { toast } from 'react-toastify';

const Settings = () => {
  const [gracePeriod, setGracePeriod] = useState(24);
  const [testMode, setTestMode] = useState(false);
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(true);

  const fetchSettings = async () => {
    try {
      const res = await axios.get<{
        gracePeriodHours: number;
        stripeTestMode: boolean;
        status: string;
      }>('/dashboard/settings');

      setGracePeriod(res.data.gracePeriodHours || 24);
      setTestMode(res.data.stripeTestMode || false);
      setStatus(res.data.status || 'OK');
    } catch {
      toast.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      await axios.put('/dashboard/settings', {
        gracePeriodHours: gracePeriod,
        stripeTestMode: testMode,
        status,
      });
      toast.success('Settings updated');
    } catch {
      toast.error('Failed to update settings');
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  if (loading) return <p className="p-6">Loading settings...</p>;

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold mb-4">Admin Settings</h1>

      {/* Grace Period */}
      <div className="space-y-2">
        <label htmlFor="gracePeriod" className="block font-semibold mb-1">
          Payment Grace Period (hours)
        </label>
        <input
          id="gracePeriod"
          type="number"
          min={0}
          title="Set the number of hours before payment is auto-released"
          placeholder="e.g. 24"
          value={gracePeriod}
          onChange={(e) => setGracePeriod(parseInt(e.target.value))}
          className="border rounded px-3 py-2 w-full max-w-xs"
        />
      </div>

      {/* Stripe Test Mode */}
      <div className="space-y-2">
        <label htmlFor="testMode" className="block font-semibold mb-1">
          Stripe Test Mode
        </label>
        <select
          id="testMode"
          title="Toggle between test mode and live mode for Stripe"
          value={testMode ? 'true' : 'false'}
          onChange={(e) => setTestMode(e.target.value === 'true')}
          className="border rounded px-3 py-2 w-full max-w-xs"
        >
          <option value="false">Live</option>
          <option value="true">Test Mode</option>
        </select>
      </div>

      {/* System Status */}
      <div className="space-y-2">
        <label htmlFor="systemStatus" className="block font-semibold mb-1">
          System Status
        </label>
        <input
          id="systemStatus"
          type="text"
          title="System-wide notice shown to users"
          placeholder="e.g. All systems operational"
          value={status}
          onChange={(e) => setStatus(e.target.value)}
          className="border rounded px-3 py-2 w-full max-w-xs"
        />
      </div>

      <button
        onClick={saveSettings}
        className="mt-6 bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700"
      >
        Save Changes
      </button>
    </div>
  );
};

export default Settings;
