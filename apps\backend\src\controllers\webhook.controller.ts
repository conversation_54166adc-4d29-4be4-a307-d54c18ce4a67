import { Request, Response } from 'express';
import Stripe from 'stripe';
import { completeJobPayment } from '../services/payment.service';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-04-30.basil',
});

/**
 * Handles incoming Stripe webhooks (e.g. payment_intent.succeeded).
 * Must use raw body middleware to function correctly.
 */
export const stripeWebhookHandler = async (
  req: Request,
  res: Response
): Promise<void> => {
  const signature = req.headers['stripe-signature'] as string;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      req.body as Buffer,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err: any) {
    console.error('❌ Stripe webhook signature verification failed:', err.message);
    res.status(400).send(`Webhook Error: ${err.message}`);
    return;
  }

  switch (event.type) {
    case 'payment_intent.succeeded': {
      const paymentIntent = event.data.object as Stripe.PaymentIntent;

      const jobId = paymentIntent.metadata?.jobId;
      if (!jobId) {
        console.warn('⚠️ Webhook received without jobId in metadata');
        res.status(400).json({ error: 'Missing jobId in payment metadata' });
        return;
      }

      try {
        await completeJobPayment(jobId);
        console.log(`✅ Payment completed and released for Job ${jobId}`);
      } catch (err: unknown) {
        if (err instanceof Error) {
          console.error(`❌ Failed to release payment for Job ${jobId}:`, err.message);
        } else {
          console.error(`❌ Unknown error releasing payment for Job ${jobId}`, err);
        }
      }

      break;
    }

    default:
      console.log(`ℹ️ Unhandled Stripe event type: ${event.type}`);
  }

  res.status(200).json({ received: true });
};
