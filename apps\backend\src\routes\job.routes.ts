// backend/src/routes/job.routes.ts

import { Router } from 'express';
import { authMiddleware } from '../middleware/auth.middleware';
import { requireRole } from '../middleware/role.middleware';
import { JobController } from '../controllers/job.controller';

const router = Router();

// 🔧 Job Creation and Listing
router.post('/',                authMiddleware, requireRole(['HOMEOWNER']), JobController.createJob);
router.get('/my-jobs',          authMiddleware, requireRole(['HOMEOWNER']), JobController.getMyJobs);
router.get('/assigned',         authMiddleware, requireRole(['TECHNICIAN']), JobController.getAssignedJobs);

// 🔧 Job Status & Detail
router.put('/:jobId/status',    authMiddleware, requireRole(['TECHNICIAN']), JobController.updateJobStatus);
router.get('/:jobId',           authMiddleware, JobController.getJobDetails);

// ✅ Job Confirmation by Homeowner
router.patch('/:jobId/confirm', authMiddleware, requireRole(['HOMEOWNER']), JobController.confirmJobCompletion);

export default router;
