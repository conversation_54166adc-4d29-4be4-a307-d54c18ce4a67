// src/api/technician.api.ts

import axiosClient from './axiosClient';

const PaymentAPI = {
  // 💳 Called by homeowner to make a direct payment for a job
  createPayment: async (jobId: string, amount: number, currency: string = 'GBP') => {
    const res = await axiosClient.post('/payments/create', {
      jobId,
      amount,
      currency,
    });
    return res.data;
  },

  // 🧾 Called to initiate Stripe payment sheet (native)
  createPaymentIntent: async () => {
    const res = await axiosClient.post('/payments/create-intent');
    return res.data; // should return { clientSecret: string }
  },

  // 📈 Called by technician to check total earnings
  getEarnings: async () => {
    const res = await axiosClient.get('/payments/earnings');
    return res.data;
  },
};

export default PaymentAPI;
