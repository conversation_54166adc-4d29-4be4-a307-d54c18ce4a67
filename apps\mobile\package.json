{"name": "ctron-mobile", "version": "1.0.0", "private": true, "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@aws-sdk/client-s3": "^3.812.0", "@aws-sdk/s3-request-presigner": "^3.812.0", "@expo/metro-runtime": "~4.0.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.2.0", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@stripe/stripe-react-native": "^0.38.6", "axios": "^1.9.0", "expo": "^52.0.46", "expo-crypto": "^14.0.2", "expo-image-picker": "~16.0.6", "expo-linking": "^7.0.5", "expo-location": "~18.0.10", "expo-notifications": "~0.29.14", "expo-secure-store": "~14.0.1", "expo-status-bar": "~2.0.1", "jwt-decode": "^4.0.0", "react": "^18.3.1", "react-dom": "18.3.1", "react-native": "^0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-picker-select": "^9.3.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.13", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@expo/metro-config": "^0.19.12", "@types/node": "^22.15.0", "@types/react": "^18.3.12", "@types/react-native": "^0.73.0", "babel-plugin-dotenv-import": "^3.0.1", "babel-plugin-module-resolver": "^5.0.2", "react-native-dotenv": "^3.4.11", "typescript": "^5.8.3"}}