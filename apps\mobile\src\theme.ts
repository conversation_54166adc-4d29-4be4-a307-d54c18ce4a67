// src/theme.ts

export const colors = {
  primary: '#004AAD',
  secondary: '#E5F0FF',
  danger: '#f44336',
  success: '#4CAF50',
  text: '#1e1e1e',
  background: '#ffffff',
  gray: '#888',
  lightGray: '#f1f1f1',
  border: '#ddd',
  white: '#ffffff', // ✅ added to fix theme.colors.white error
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  m: 16, // ✅ added to fix theme.spacing.m error
};

export const typography = {
  fontSize: {
    sm: 14,
    base: 16,
    lg: 20,
    xl: 24,
  },
  fontWeight: {
    regular: '400',
    medium: '500',
    bold: '700',
  },
};

export const fonts = {
  regular: 'Poppins-Regular',
  medium: 'Poppins-Medium',
  bold: 'Poppins-Bold',
};

export const theme = {
  colors,
  spacing,
  typography,
  fonts,
};

export type Theme = typeof theme;
