// apps/mobile/src/navigation/TechnicianStack.tsx

import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import TechnicianDrawerNavigator from './TechnicianDrawerNavigator';
import AssignedJobsScreen from '../screens/Technician/AssignedJobsScreen';
import ActiveJobScreen from '../screens/Technician/ActiveJobScreen';
import EarningsScreen from '../screens/Technician/EarningsScreen';
import AcceptJobScreen from '../screens/Technician/AcceptJobScreen';
import JobDetailsScreen from '../screens/Technician/JobDetailsScreen';

export type TechnicianStackParamList = {
  TechnicianDrawer: undefined;
  AssignedJobs: undefined;
  ActiveJob: undefined;
  Earnings: undefined;
  AcceptJob: undefined;
  JobDetailsScreen: { jobId: string };
};

const Stack = createNativeStackNavigator<TechnicianStackParamList>();

const TechnicianStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="TechnicianDrawer" component={TechnicianDrawerNavigator} />
      <Stack.Screen name="AssignedJobs" component={AssignedJobsScreen} />
      <Stack.Screen name="ActiveJob" component={ActiveJobScreen} />
      <Stack.Screen name="Earnings" component={EarningsScreen} />
      <Stack.Screen name="AcceptJob" component={AcceptJobScreen} />
      <Stack.Screen name="JobDetailsScreen" component={JobDetailsScreen} />
    </Stack.Navigator>
  );
};

export default TechnicianStack;
