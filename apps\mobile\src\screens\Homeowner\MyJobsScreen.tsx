//apps/mobile/src/screens/Homeowner/MyJobsScreen.tsx
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  StyleSheet,
  RefreshControl,
} from 'react-native';
import { useAuth } from '../../context/AuthContext';
import axios from '../../services/api';
import Toast from 'react-native-toast-message';

type Job = {
  id: string;
  issue: string;
  status: string;
  scheduledAt: string;
  createdAt: string;
  technician?: {
    user: {
      fullName: string;
    };
  };
};

const MyJobsScreen: React.FC = () => {
  const { token } = useAuth();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchJobs = async () => {
    try {
      const res = await axios.get<{ jobs: Job[] }>('/jobs/my-jobs', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setJobs(res.data.jobs);
    } catch (err) {
      Toast.show({
        type: 'error',
        text1: 'Failed to fetch jobs',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    fetchJobs();
  };

  const renderJob = ({ item }: { item: Job }) => (
    <View style={styles.card}>
      <Text style={styles.issue}>{item.issue}</Text>
      <Text style={styles.label}>Status: <Text style={styles.value}>{item.status}</Text></Text>
      <Text style={styles.label}>
        Scheduled: <Text style={styles.value}>{new Date(item.scheduledAt).toLocaleString()}</Text>
      </Text>
      {item.technician && (
        <Text style={styles.label}>
          Technician: <Text style={styles.value}>{item.technician.user.fullName}</Text>
        </Text>
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loader}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={jobs}
        keyExtractor={(item) => item.id}
        renderItem={renderJob}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <Text style={styles.empty}>No jobs found. Book a service to get started.</Text>
        }
        contentContainerStyle={jobs.length === 0 ? styles.emptyContainer : undefined}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f4f6f8',
    padding: 16,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    backgroundColor: '#fff',
    padding: 14,
    borderRadius: 10,
    marginBottom: 12,
    elevation: 2,
  },
  issue: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 6,
  },
  label: {
    fontSize: 14,
    color: '#444',
  },
  value: {
    fontWeight: '500',
    color: '#000',
  },
  empty: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginTop: 40,
  },
  emptyContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
});

export default MyJobsScreen;
