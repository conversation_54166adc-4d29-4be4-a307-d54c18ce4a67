//backend/src/routes/auth.routes.ts
import express from 'express';
import { AuthController } from '../controllers/auth.controller';
import { wrapAsync } from '../utils/wrapAsync';
import { authMiddleware } from '../middleware/auth.middleware'; // ✅ lowercase a

const router = express.Router();

// 🔐 Auth Routes
router.post('/signup', wrapAsync(AuthController.signup));
router.post('/login', wrapAsync(AuthController.login));
router.get('/me', authMiddleware, wrapAsync(AuthController.getProfile)); // ✅ Protect with auth

export default router;
