// 📁 File: apps/web/src/hooks/useSocket.ts

import { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';

const SOCKET_URL = 'http://localhost:5000'; // Update to match your backend

let socket: Socket | null = null;

const useSocket = (onTechnicianUpdate: (data: any) => void) => {
  const isConnected = useRef(false);

  useEffect(() => {
    if (!isConnected.current) {
      const token = localStorage.getItem('token');

      socket = io(SOCKET_URL, {
        auth: { token },
        transports: ['websocket'],
      });

      socket.on('connect', () => {
        console.log('[Socket] Connected:', socket?.id);
        isConnected.current = true;
      });

      socket.on('disconnect', () => {
        console.log('[Socket] Disconnected');
        isConnected.current = false;
      });

      socket.on('technician_update', onTechnicianUpdate);
    }

    return () => {
      socket?.off('technician_update', onTechnicianUpdate);
    };
  }, [onTechnicianUpdate]);
};

export default useSocket;
