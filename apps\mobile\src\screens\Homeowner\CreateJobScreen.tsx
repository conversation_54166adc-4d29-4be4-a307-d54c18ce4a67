//mobile/src/screens/Homeowner/CreateJobScreen.tsx
import React, { useEffect, useState } from 'react';
import { View, Text, TextInput, Button, Image, ScrollView, Switch, Alert, ActivityIndicator, StyleSheet } from 'react-native';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';
import RNPickerSelect from 'react-native-picker-select';
import axios from 'axios';
import { useNavigation } from '@react-navigation/native';

interface Technician {
  id: string;
  fullName: string;
  rating: number;
  distance: number;
}

interface Coordinates {
  latitude: number;
  longitude: number;
}

interface SelectedImage {
  uri: string;
  type: string;
}

export default function CreateJobScreen() {
  const navigation = useNavigation();

  const [useLocation, setUseLocation] = useState(true);
  const [location, setLocation] = useState<Coordinates | null>(null);
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [selectedTechnician, setSelectedTechnician] = useState<string | null>(null);
  const [minRating, setMinRating] = useState(false);
  const [issue, setIssue] = useState('');
  const [scheduledAt, setScheduledAt] = useState('');
  const [image, setImage] = useState<SelectedImage | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (useLocation) {
      (async () => {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission denied', 'Location access is required to find nearby technicians.');
          return;
        }
        const loc = await Location.getCurrentPositionAsync({});
        setLocation({ latitude: loc.coords.latitude, longitude: loc.coords.longitude });
      })();
    }
  }, [useLocation]);

  useEffect(() => {
    if (location) fetchTechnicians();
  }, [location, minRating]);

  const fetchTechnicians = async () => {
    try {
      const { data } = await axios.get('/api/technicians/nearby', {
        params: {
          lat: location?.latitude,
          lng: location?.longitude,
          minRating: minRating ? 4.5 : 0,
        },
      });
      setTechnicians(data.technicians);
    } catch (err) {
      Alert.alert('Error', 'Failed to fetch technicians');
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 0.7,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      setImage({
        uri: result.assets[0].uri,
        type: result.assets[0].type || 'image/jpeg',
      });
    }
  };

  const handleSubmit = async () => {
    if (!issue || !scheduledAt || !selectedTechnician) {
      Alert.alert('Missing fields', 'Please fill out all required fields.');
      return;
    }

    try {
      setLoading(true);
      let photoUrl = null;

      if (image) {
        const presign = await axios.post('/api/uploads/presign', {
          fileType: image.type,
        });

        await fetch(presign.data.url, {
          method: 'PUT',
          body: await fetch(image.uri).then(r => r.blob()),
          headers: { 'Content-Type': image.type },
        });

        photoUrl = presign.data.key;
      }

      await axios.post('/api/jobs', {
        technicianId: selectedTechnician,
        issue,
        scheduledAt,
        photoUrl,
        latitude: useLocation && location ? location.latitude : null,
        longitude: useLocation && location ? location.longitude : null,
      });

      Alert.alert('Success', 'Job created successfully');
      navigation.goBack();
    } catch (err) {
      Alert.alert('Error', 'Failed to create job');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView contentContainerStyle={{ padding: 20 }}>
      <Text style={styles.label}>Use My Location</Text>
      <Switch value={useLocation} onValueChange={setUseLocation} />

      <View style={{ marginVertical: 16 }}>
        <Text style={styles.label}>Minimum 4.5★ Technicians Only</Text>
        <Switch value={minRating} onValueChange={setMinRating} />
      </View>

      <Text style={styles.label}>Select Technician</Text>
      <RNPickerSelect
        onValueChange={value => setSelectedTechnician(value)}
        items={technicians.map(t => ({
          label: `${t.fullName} (${t.rating || 'N/A'}★, ${t.distance.toFixed(1)} km)`,
          value: t.id,
        }))}
        placeholder={{ label: 'Select a technician...', value: null }}
      />

      <Text style={[styles.label, { marginTop: 16 }]}>Issue Description</Text>
      <TextInput
        style={styles.input}
        placeholder="Describe the problem..."
        multiline
        value={issue}
        onChangeText={setIssue}
      />

      <Text style={styles.label}>Scheduled Date & Time</Text>
      <TextInput
        style={styles.input}
        placeholder="YYYY-MM-DDTHH:MM"
        value={scheduledAt}
        onChangeText={setScheduledAt}
      />

      <Button title="Upload Photo (optional)" onPress={pickImage} />
      {image && <Image source={{ uri: image.uri }} style={{ width: '100%', height: 200, marginVertical: 10 }} />}

      <Button title={loading ? 'Submitting...' : 'Create Job'} onPress={handleSubmit} disabled={loading} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 10,
    marginBottom: 16,
    borderRadius: 6,
  },
});
