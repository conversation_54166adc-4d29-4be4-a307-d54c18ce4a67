// src/screens/Homeowner/JobDetailsScreen.tsx

import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { useRoute } from '@react-navigation/native';
import { JobAPI } from '../../api/job.api';

const JobDetailsScreen = () => {
  const route = useRoute<any>();
  const { jobId } = route.params;
  const [job, setJob] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const fetchJobDetails = async () => {
    try {
      const res = await JobAPI.getJobDetails(jobId);
      setJob(res);
    } catch (err: any) {
      console.error(err);
      Alert.alert('Error', 'Failed to fetch job details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchJobDetails();
  }, []);

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#004AAD" />
      </View>
    );
  }

  if (!job) {
    return (
      <View style={styles.container}>
        <Text style={styles.error}>Job not found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Job #{job.id.slice(0, 8)}</Text>
      <Text style={styles.label}>Status:</Text>
      <Text style={styles.value}>{job.status}</Text>

      <Text style={styles.label}>Issue:</Text>
      <Text style={styles.value}>{job.issue}</Text>

      <Text style={styles.label}>Scheduled At:</Text>
      <Text style={styles.value}>
        {new Date(job.scheduledAt).toLocaleString()}
      </Text>

      <Text style={styles.label}>Technician ID:</Text>
      <Text style={styles.value}>{job.technicianId}</Text>
    </View>
  );
};

export default JobDetailsScreen;

const styles = StyleSheet.create({
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  container: {
    flex: 1,
    padding: 24,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  label: {
    marginTop: 12,
    fontWeight: '600',
  },
  value: {
    fontSize: 16,
    marginTop: 4,
    color: '#333',
  },
  error: {
    fontSize: 16,
    color: 'red',
    textAlign: 'center',
    marginTop: 32,
  },
});
