{
  "compilerOptions": {
    "target": "ES2021",
    "module": "commonjs",
    "strict": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
    "outDir": "dist",
    "baseUrl": "./src",
    "resolveJsonModule": true,
    "types": ["node"]    // ✅ Add Node.js built-ins like process.exit()
  },
  "include": [
    "src/**/*.ts",
    "prisma/**/*.ts"     // ✅ Also include seed.ts
  ],
  "exclude": ["node_modules", "dist"]
}
