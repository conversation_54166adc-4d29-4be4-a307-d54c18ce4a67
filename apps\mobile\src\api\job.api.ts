// src/api/job.api.ts
import axiosClient from './axiosClient';
import type { Job } from '../types/job';

export type JobCreatePayload = {
  issue: string;
  scheduledAt: string;
  technicianId?: string;
};

export const JobAPI = {
  createJob: async (data: JobCreatePayload): Promise<Job> => {
    const res = await axiosClient.post('/jobs', data);
    return res.data;
  },

  getUserJobs: async (): Promise<Job[]> => {
    const res = await axiosClient.get('/jobs/my-jobs');
    return res.data.jobs;
  },

  getTechnicianJobs: async (): Promise<Job[]> => {
    const res = await axiosClient.get('/jobs/assigned');
    return res.data.jobs;
  },

  getJobDetails: async (id: string): Promise<Job> => {
    const res = await axiosClient.get(`/jobs/${id}`);
    return res.data.job;
  },

  updateStatus: async (
    id: string,
    status: 'IN_PROGRESS' | 'COMPLETED',
    payload?: { photoUrl?: string }
  ): Promise<Job> => {
    const res = await axiosClient.put(`/jobs/${id}/status`, {
      status,
      ...(payload || {}),
    });
    return res.data.job;
  },
};
