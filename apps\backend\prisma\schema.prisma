generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  fullName  String
  password  String
  phone     String?
  role      Role
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 🔁 Relations
  technician Technician?
  jobs       Job[]       @relation("JobUser")
  reviews    Review[]
  payments   Payment[]
}

model Technician {
  id             String   @id @default(uuid())
  userId         String   @unique
  specialization String
  isAvailable    Boolean  @default(true)
  rating         Float?
  latitude       Float?
  longitude      Float?
  createdAt      DateTime @default(now())

  // 🔁 Relations
  user    User     @relation(fields: [userId], references: [id])
  jobs    Job[]    @relation("JobTechnician")
  reviews Review[]
}

model Job {
  id              String     @id @default(uuid())
  issue           String
  latitude        Float?
  longitude       Float?
  photoUrl        String?
  proofImageKey   String?
  status          JobStatus  @default(PENDING)
  scheduledAt     DateTime
  createdAt       DateTime   @default(now())
  completedAt     DateTime?
  confirmedAt     DateTime?

  // 🔁 Relations
  userId          String
  user            User        @relation("JobUser", fields: [userId], references: [id])

  technicianId    String?
  technician      Technician? @relation("JobTechnician", fields: [technicianId], references: [id])

  review          Review?
  payment         Payment?
}

model Payment {
  id                    String   @id @default(uuid())
  jobId                 String   @unique
  userId                String   // ✅ moved up for clarity
  amount                Float
  currency              String   @default("GBP")
  stripePaymentIntentId String   @unique
  isReleased            Boolean  @default(false)
  releasedAt            DateTime?
  isFrozen              Boolean  @default(false)
  freezeReason          String?
  createdAt             DateTime @default(now())

  // 🔁 Relations
  job   Job  @relation(fields: [jobId], references: [id])
  user  User @relation(fields: [userId], references: [id])
}

model Review {
  id            String     @id @default(uuid())
  userId        String
  user          User       @relation(fields: [userId], references: [id])

  jobId         String     @unique
  job           Job        @relation(fields: [jobId], references: [id])

  rating        Int
  comment       String?
  createdAt     DateTime   @default(now())

  technicianId  String?
  technician    Technician? @relation(fields: [technicianId], references: [id])
}

model Setting {
  id               String   @id @default(cuid())
  gracePeriodHours Int      @default(24)
  stripeTestMode   Boolean  @default(false)
  statusMessage    String   @default("System operational")
  updatedAt        DateTime @updatedAt
}

enum Role {
  HOMEOWNER
  TECHNICIAN
}

enum JobStatus {
  PENDING
  ACCEPTED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}
