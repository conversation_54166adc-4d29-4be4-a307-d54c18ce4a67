// backend/src/schedulers/autoReleaseScheduler.ts
import cron from 'node-cron';
import <PERSON><PERSON> from 'stripe';
import { prisma } from '../config/db';
import { env } from '../config/env';

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-04-30.basil',
});

const runAutoRelease = async () => {
  const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24h ago

  const jobs = await prisma.job.findMany({
    where: {
      status: 'COMPLETED',
      completedAt: { lt: cutoff },
      payment: {
        isReleased: false,
      },
    },
    include: {
      payment: true,
    },
  });

  if (jobs.length === 0) return;

  for (const job of jobs) {
    const intentId = job.payment?.stripePaymentIntentId;

    try {
      if (!intentId) throw new Error('Missing PaymentIntent');

      await stripe.paymentIntents.capture(intentId);

      await prisma.payment.update({
        where: { jobId: job.id },
        data: {
          isReleased: true,
          releasedAt: new Date(),
        },
      });

      await prisma.job.update({
        where: { id: job.id },
        data: {
          confirmedAt: new Date(),
        },
      });

      console.log(`✅ Auto-released payment for job ${job.id}`);
    } catch (err) {
      console.error(`❌ Failed to auto-release for job ${job.id}`, err);
    }
  }
};

// 🕓 Schedule: runs every day at 1:00 AM
cron.schedule('0 1 * * *', () => {
  console.log('🕓 Running daily auto-release task...');
  runAutoRelease();
});
