// src/api/technician.api.ts

import axiosClient from './axiosClient';

export const TechnicianAPI = {
  onboard: async (specialization: string) => {
    const res = await axiosClient.post('/technician/onboard', { specialization });
    return res.data;
  },

  getDashboard: async () => {
    const res = await axiosClient.get('/technician/dashboard');
    return res.data;
  },

  updateAvailability: async (isAvailable: boolean) => {
    const res = await axiosClient.patch('/technician/availability', { isAvailable });
    return res.data;
  },

  // ✅ ADD THIS
  getProfile: async () => {
    const res = await axiosClient.get('/technician/profile');
    return res.data;
  },
};
