//web/src/components/Sidebar.tsx
import { Link, useLocation } from 'react-router-dom';

const Sidebar = () => {
  const location = useLocation();

  const linkClass = (path: string) =>
    `block py-2 px-4 rounded font-medium transition ${
      location.pathname === path
        ? 'bg-blue-600 text-white'
        : 'text-gray-700 hover:bg-gray-200'
    }`;

  return (
    <aside className="w-64 h-screen bg-gray-100 p-4 border-r">
      <h2 className="text-lg font-bold mb-6">CTRON Admin</h2>
      <nav className="space-y-2">
        <Link to="/dashboard" className={linkClass('/dashboard')}>
          Dashboard
        </Link>
        <Link to="/technicians" className={linkClass('/technicians')}>
          Technicians
        </Link>
      </nav>
    </aside>
  );
};

export default Sidebar;
