// 📁 File: apps/web/src/pages/admin/Technicians.tsx

import { useEffect, useState, useCallback } from 'react';
import axios from '../../services/api';
import useSocket from '../../hooks/useSocket';

type Technician = {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  kycStatus: string;
};

const Technicians = () => {
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch initial list of pending technicians
  const fetchTechnicians = useCallback(async () => {
    try {
      const res = await axios.get<Technician[]>('/technicians/pending');
      setTechnicians(res.data);
    } catch (err) {
      console.error('Failed to fetch technicians', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchTechnicians();
  }, [fetchTechnicians]);

  // Handle technician update from socket
  const handleTechnicianUpdate = useCallback((updatedTech: Technician) => {
    setTechnicians((prev) => {
      const exists = prev.find((t) => t.id === updatedTech.id);
      if (!exists && updatedTech.kycStatus === 'PENDING') {
        return [...prev, updatedTech];
      }
      return prev.map((t) => (t.id === updatedTech.id ? updatedTech : t));
    });
  }, []);

  // Set up socket listener
  useSocket(handleTechnicianUpdate);

  // Approve technician via API
  const approveTechnician = async (id: string) => {
    try {
      await axios.patch(`/technicians/${id}/approve`);
      setTechnicians((prev) => prev.filter((t) => t.id !== id));
    } catch (err) {
      console.error('Approval failed:', err);
    }
  };

  return (
    <div className="p-6">
      <h2 className="text-xl font-bold mb-4">Pending Technicians</h2>
      {loading ? (
        <p>Loading...</p>
      ) : technicians.length === 0 ? (
        <p className="text-gray-600">No pending technicians.</p>
      ) : (
        <ul className="space-y-4">
          {technicians.map((tech) => (
            <li
              key={tech.id}
              className="bg-white rounded shadow p-4 flex justify-between items-center"
            >
              <div>
                <p className="font-semibold">{tech.fullName}</p>
                <p className="text-sm">{tech.email}</p>
                <p className="text-sm text-gray-500">{tech.phone}</p>
              </div>
              <button
                onClick={() => approveTechnician(tech.id)}
                className="bg-green-600 text-white px-4 py-1 rounded"
              >
                Approve
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default Technicians;
