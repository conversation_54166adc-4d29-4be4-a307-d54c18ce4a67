/// <reference types="node" />
import 'dotenv/config';

export default {
  expo: {
    name: 'CTRON Home',
    slug: 'ctron-home',
    scheme: 'ctronhome',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './assets/icon.png',
    userInterfaceStyle: 'light',
    splash: {
      image: './assets/splash.png',
      resizeMode: 'contain',
      backgroundColor: '#ffffff'
    },
    extra: {
      API_URL: process.env.API_URL || 'http://localhost:4000/api'
    },
    ios: {
      supportsTablet: true
    },
    android: {
      adaptiveIcon: {
        foregroundImage: './assets/adaptive-icon.png',
        backgroundColor: '#ffffff'
      }
    },
    web: {
      favicon: './assets/favicon.png'
    },
    plugins: ['expo-secure-store']
  }
};
