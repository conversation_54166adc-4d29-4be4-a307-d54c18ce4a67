// backend/src/models/job.model.ts

import { prisma } from '../config/db';
import { JobStatus } from '../generated/prisma';

export const JobModel = {
  /**
   * Create a new job
   */
  createJob: async (data: {
    userId: string;
    technicianId: string;
    issue: string;
    photoUrl?: string;
    scheduledAt: Date;
  }) => {
    return prisma.job.create({
      data: {
        userId: data.userId,
        technicianId: data.technicianId,
        issue: data.issue,
        photoUrl: data.photoUrl || null,
        scheduledAt: data.scheduledAt,
        status: JobStatus.PENDING,
      },
    });
  },

  /**
   * Get all jobs created by a user (homeowner)
   */
  getJobsByUser: async (userId: string) => {
    return prisma.job.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      include: {
        technician: {
          include: {
            user: true,
          },
        },
      },
    });
  },

  /**
   * Get all jobs assigned to a technician
   */
  getJobsByTechnician: async (technicianId: string) => {
    return prisma.job.findMany({
      where: { technicianId },
      orderBy: { createdAt: 'desc' },
      include: {
        user: true,
      },
    });
  },

  /**
   * Update the status of a job (basic)
   */
  updateStatus: async (jobId: string, status: JobStatus) => {
    return prisma.job.update({
      where: { id: jobId },
      data: { status },
    });
  },

  /**
   * Update job status and optionally save photoUrl or proofImageKey
   */
  updateStatusWithProof: async (
    jobId: string,
    status: JobStatus,
    imageKey?: string // proofImageKey (preferred) or photoUrl (fallback)
  ) => {
    return prisma.job.update({
      where: { id: jobId },
      data: {
        status,
        // If the status is COMPLETED and imageKey is provided,
        // we save it in the appropriate field
        ...(status === JobStatus.COMPLETED && imageKey
          ? imageKey.startsWith('uploads/')
            ? { proofImageKey: imageKey }
            : { photoUrl: imageKey }
          : {}),
        completedAt: status === JobStatus.COMPLETED ? new Date() : undefined,
      },
    });
  },

  /**
   * Get full job details
   */
  getJobById: async (jobId: string) => {
    return prisma.job.findUnique({
      where: { id: jobId },
      include: {
        user: true,
        technician: true,
        payment: true,
        review: true,
      },
    });
  },
};
