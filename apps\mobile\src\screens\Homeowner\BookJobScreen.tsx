// src/screens/Homeowner/BookJobScreen.tsx
import React, { useState } from 'react';
import {
  Alert,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { JobAPI, JobCreatePayload } from '../../api/job.api';

let DateTimePicker: any;
try {
  DateTimePicker = require('@react-native-community/datetimepicker').default;
} catch (err) {
  DateTimePicker = null;
}

export default function BookJobScreen() {
  const nav = useNavigation<any>();

  const [technicianId, setTechnicianId] = useState('');
  const [issue, setIssue] = useState('');
  const [scheduledAt, setScheduledAt] = useState<Date>(new Date());
  const [scheduledText, setScheduledText] = useState('');
  const [showPicker, setShowPicker] = useState(false);
  const [loading, setLoading] = useState(false);

  const onDate = (_: any, date?: Date) => {
    setShowPicker(false);
    if (date) {
      setScheduledAt(date);
      setScheduledText(date.toLocaleString());
    }
  };

  const handleBook = async () => {
    if (!issue) {
      Alert.alert('Validation', 'Please describe the issue.');
      return;
    }

    if (!scheduledAt) {
      Alert.alert('Validation', 'Please pick a schedule date.');
      return;
    }

    const payload: JobCreatePayload = {
      issue,
      scheduledAt: scheduledAt.toISOString(),
      ...(technicianId ? { technicianId } : {}),
    };

    try {
      setLoading(true);
      await JobAPI.createJob(payload);
      Alert.alert('Success', 'Job booked!');
      nav.navigate('Home');
    } catch (e: any) {
      console.error(e);
      Alert.alert('Error', e?.response?.data?.message || 'Booking failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Technician ID (optional)</Text>
      <TextInput
        style={styles.input}
        value={technicianId}
        onChangeText={setTechnicianId}
        placeholder="e.g. tech_01"
      />

      <Text style={styles.label}>Issue</Text>
      <TextInput
        style={[styles.input, styles.textArea]}
        multiline
        value={issue}
        onChangeText={setIssue}
        placeholder="Describe the problem…"
      />

      <Text style={styles.label}>Schedule</Text>

      {DateTimePicker ? (
        <>
          <TouchableOpacity
            style={styles.dateBtn}
            onPress={() => setShowPicker(true)}
          >
            <Text>{scheduledAt.toLocaleString()}</Text>
          </TouchableOpacity>

          {showPicker && Platform.OS !== 'web' && (
            <DateTimePicker
              mode="datetime"
              value={scheduledAt}
              onChange={onDate}
            />
          )}
        </>
      ) : (
        <TextInput
          style={styles.input}
          value={scheduledText}
          onChangeText={(text) => {
            setScheduledText(text);
            const parsed = new Date(text);
            if (!isNaN(parsed.getTime())) {
              setScheduledAt(parsed);
            }
          }}
          placeholder="e.g. 2025-06-01 14:30"
        />
      )}

      <TouchableOpacity
        style={[styles.bookBtn, loading && { opacity: 0.6 }]}
        disabled={loading}
        onPress={handleBook}
      >
        <Text style={styles.bookTxt}>
          {loading ? 'Booking…' : 'Confirm & Pay'}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 24 },
  label: { marginTop: 16, fontWeight: '600' },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 12,
    borderRadius: 6,
    marginTop: 6,
  },
  textArea: { height: 90 },
  dateBtn: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 12,
    borderRadius: 6,
    marginTop: 6,
  },
  bookBtn: {
    backgroundColor: '#0f8',
    padding: 16,
    borderRadius: 8,
    marginTop: 32,
    alignItems: 'center',
  },
  bookTxt: { fontWeight: '700' },
});
