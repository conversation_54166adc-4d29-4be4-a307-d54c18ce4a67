# 🔁 Node.js & TypeScript (Backend)
node_modules/
dist/
.env
.env.*.local
package-lock.json
npm-debug.log*
*.tsbuildinfo
coverage/
build/

# ✅ Prisma
apps/backend/prisma/dev.db
apps/backend/prisma/dev.db-journal

# 📱 Expo React Native (Mobile App)
apps/mobile/node_modules/
apps/mobile/.env
apps/mobile/.expo/
apps/mobile/.expo-shared/
apps/mobile/package-lock.json
apps/mobile/coverage/
apps/mobile/*.tsbuildinfo
apps/mobile/assets/**/*.db
apps/mobile/*.log
apps/mobile/*.tgs

# 💡 IDE/editor configs
.vscode/
.idea/
.DS_Store
Thumbs.db

# 🚫 OS-level junk
*.swp
*.swo
*.bak

# General fallback
.expo
.expo*
.next
a p p s / b a c k e n d / c t r o n - b a c k e n d - u p d a t e d . z i p 
 
 