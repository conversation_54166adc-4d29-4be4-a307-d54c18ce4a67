import axios from 'axios';

interface SignupPayload {
  fullName: string;
  email: string;
  phone: string;
  password: string;
  role: 'HOMEOWNER' | 'TECHNICIAN';
}

interface LoginPayload {
  email: string;
  password: string;
}

interface AuthResponse {
  token: string;
}

// ✅ Use the Expo public env variable correctly
const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL;

const AuthAPI = {
  signup: async (payload: SignupPayload): Promise<AuthResponse> => {
    const response = await axios.post(`${API_BASE_URL}/api/auth/signup`, payload);
    return response.data;
  },

  login: async (payload: LoginPayload): Promise<AuthResponse> => {
    const response = await axios.post(`${API_BASE_URL}/api/auth/login`, payload);
    return response.data;
  },
};

export default AuthAPI;
