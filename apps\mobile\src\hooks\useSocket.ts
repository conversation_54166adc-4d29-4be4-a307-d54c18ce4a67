// src/hooks/useSocket.ts

import { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useUser } from './useUser';

export const useSocket = () => {
  const { user } = useUser(); // Get user (with token) from hook
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    if (user?.token && !socketRef.current) {
      socketRef.current = io('http://localhost:5000', {
        auth: {
          token: user.token,
        },
      });

      socketRef.current.on('connect', () => {
        console.log('🟢 Connected to socket server');
      });

      socketRef.current.on('disconnect', () => {
        console.log('🔴 Disconnected from socket server');
      });
    }

    return () => {
      socketRef.current?.disconnect();
      socketRef.current = null;
    };
  }, [user?.token]);

  return socketRef.current;
};
