// src/sockets/technician.socket.ts

import { Server, Socket } from 'socket.io';

export const registerTechnicianSockets = (io: Server, socket: Socket) => {
  socket.on('goOnline', (technicianId: string) => {
    socket.join(`tech:${technicianId}`);
    io.to(`tech:${technicianId}`).emit('statusUpdate', { online: true });
  });

  socket.on('goOffline', (technicianId: string) => {
    socket.leave(`tech:${technicianId}`);
    io.to(`tech:${technicianId}`).emit('statusUpdate', { online: false });
  });
};
