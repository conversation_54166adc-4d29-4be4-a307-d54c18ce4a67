// 📁 File: apps/web/src/App.tsx

import { Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/Login';
import Dashboard from './pages/admin/Dashboard';
import Technicians from './pages/admin/Technicians';
import Assistant from './pages/admin/Assistant'; // ✅ New GPT Assistant Page

import Sidebar from './components/Sidebar';
import PrivateRoute from './components/PrivateRoute';
import RoleRoute from './components/RoleRoute';

const App = () => {
  return (
    <Routes>
      {/* Public route */}
      <Route path="/login" element={<Login />} />

      {/* Protected route: Any authenticated user */}
      <Route
        path="/dashboard"
        element={
          <PrivateRoute>
            <div className="flex">
              <Sidebar />
              <main className="flex-1 p-4">
                <Dashboard />
              </main>
            </div>
          </PrivateRoute>
        }
      />

      {/* Protected route: Admin only */}
      <Route
        path="/technicians"
        element={
          <RoleRoute allowedRoles={['ADMIN']}>
            <div className="flex">
              <Sidebar />
              <main className="flex-1 p-4">
                <Technicians />
              </main>
            </div>
          </RoleRoute>
        }
      />

      {/* ✅ New: Assistant page for Admin */}
      <Route
        path="/assistant"
        element={
          <RoleRoute allowedRoles={['ADMIN']}>
            <div className="flex">
              <Sidebar />
              <main className="flex-1 p-4">
                <Assistant />
              </main>
            </div>
          </RoleRoute>
        }
      />

      {/* Fallback: redirect unknown routes */}
      <Route path="*" element={<Navigate to="/login" />} />
    </Routes>
  );
};

export default App;
